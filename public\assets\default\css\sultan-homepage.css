/* Sultan Educational Platform - Homepage Styles */

/* Hero Section Styles */
.sultan-hero-section {
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.05) 0%, rgba(var(--secondary-rgb), 0.05) 100%);
    padding: 80px 0;
    overflow: hidden;
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-title {
    font-size: 3.5rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    color: #6c757d;
}

.hero-buttons .btn {
    margin: 0.5rem;
    padding: 12px 24px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.hero-image img {
    border: 8px solid rgba(255,255,255,0.8);
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.hero-image:hover img {
    transform: scale(1.05);
}

/* Hero Decorations */
.hero-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.decoration-1 {
    position: absolute;
    top: 10%;
    right: 5%;
    width: 100px;
    height: 100px;
    background: rgba(var(--primary-rgb), 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.decoration-2 {
    position: absolute;
    bottom: 10%;
    left: 5%;
    width: 80px;
    height: 80px;
    background: rgba(var(--secondary-rgb), 0.1);
    transform: rotate(45deg);
    animation: float 4s ease-in-out infinite reverse;
}

.decoration-3 {
    position: absolute;
    top: 50%;
    left: 10%;
    width: 60px;
    height: 60px;
    background: rgba(var(--success-rgb), 0.1);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: float 5s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Demo Videos Section */
.demo-video-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.demo-video-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.demo-video-card:hover .video-thumbnail img {
    transform: scale(1.1);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(var(--primary-rgb), 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: var(--primary);
    transform: translate(-50%, -50%) scale(1.1);
    color: white;
}

.video-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

/* Why Choose Section */
.feature-box {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--dark);
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
}

/* About Teacher Section */
.sultan-about-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.teacher-profile-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.teacher-profile-card:hover {
    transform: translateY(-10px);
}

.teacher-avatar {
    width: 200px;
    height: 200px;
    margin: 0 auto 30px;
    border: 8px solid rgba(var(--primary-rgb), 0.1);
    border-radius: 50%;
    overflow: hidden;
    position: relative;
}

.teacher-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.teacher-badges {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.teacher-badge {
    background: var(--primary);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.cv-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
}

.cv-item {
    border-left: 3px solid var(--primary);
    padding-left: 20px;
    margin-bottom: 25px;
    position: relative;
}

.cv-item::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 0;
    width: 13px;
    height: 13px;
    background: var(--primary);
    border-radius: 50%;
}

.cv-year {
    color: var(--primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.cv-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--dark);
}

.cv-description {
    color: #6c757d;
    font-size: 0.95rem;
}

/* Student Reviews */
.review-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    margin: 15px;
    transition: transform 0.3s ease;
}

.review-card:hover {
    transform: translateY(-5px);
}

.review-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.review-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-left: 15px;
    object-fit: cover;
}

.review-name {
    font-weight: 600;
    margin-bottom: 2px;
    color: var(--dark);
}

.review-course {
    font-size: 0.85rem;
    color: #6c757d;
}

.review-rating {
    margin-bottom: 15px;
}

.review-text {
    color: #495057;
    line-height: 1.6;
    font-style: italic;
}

/* Course Cards */
.course-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.course-image-wrapper {
    position: relative;
    overflow: hidden;
}

.course-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-image {
    transform: scale(1.1);
}

.course-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 2;
}

.course-stats {
    border-top: 1px solid #eee;
    padding-top: 15px;
    margin-top: 15px;
}

.course-price .current-price {
    font-size: 1.25rem;
    font-weight: 700;
}

.course-price .old-price {
    font-size: 1rem;
    margin-right: 8px;
}

/* Sales Location Cards */
.sales-location-card {
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.sales-location-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.location-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.location-details {
    font-size: 0.95rem;
}

.location-details .d-flex {
    align-items: center;
}

/* Final CTA Section */
.sultan-final-cta {
    background: linear-gradient(135deg, var(--primary) 0%, #1e3c72 100%);
    position: relative;
    overflow: hidden;
}

.cta-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta-description {
    font-size: 1.1rem;
    line-height: 1.7;
    opacity: 0.9;
}

.cta-features {
    margin: 30px 0;
}

.cta-features .d-flex {
    margin-bottom: 10px;
}

.cta-buttons .btn {
    padding: 15px 30px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* CTA Decorations */
.cta-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.decoration-circle-1 {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
}

.decoration-circle-2 {
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background: rgba(255,255,255,0.05);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite reverse;
}

.decoration-triangle {
    position: absolute;
    top: 50%;
    right: 10%;
    width: 0;
    height: 0;
    border-left: 50px solid rgba(255,255,255,0.1);
    border-top: 30px solid transparent;
    border-bottom: 30px solid transparent;
    animation: float 7s ease-in-out infinite;
}

/* Common Section Styles */
.home-sections {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 15px;
}

.section-hint {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 50px;
}

/* Responsive Design */
@media (max-width: 1199px) {
    .hero-title {
        font-size: 3rem;
    }
    
    .section-title {
        font-size: 2.2rem;
    }
}

@media (max-width: 991px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .home-sections {
        padding: 60px 0;
    }
    
    .teacher-profile-card {
        padding: 30px;
    }
    
    .teacher-avatar {
        width: 150px;
        height: 150px;
    }
}

@media (max-width: 767px) {
    .hero-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .home-sections {
        padding: 40px 0;
    }
    
    .feature-box {
        padding: 25px 15px;
        margin-bottom: 20px;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .teacher-profile-card {
        padding: 20px;
    }
    
    .teacher-avatar {
        width: 120px;
        height: 120px;
        margin-bottom: 20px;
    }
    
    .cta-title {
        font-size: 2rem;
    }
    
    .cta-buttons .btn {
        font-size: 1rem;
        padding: 12px 24px;
    }
}

@media (max-width: 575px) {
    .hero-title {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 1.6rem;
    }
    
    .cta-title {
        font-size: 1.8rem;
    }
}

/* Utility Classes */
.text-sm {
    font-size: 0.875rem;
}

.opacity-90 {
    opacity: 0.9;
}

.opacity-75 {
    opacity: 0.75;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 1s ease-in-out;
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .course-card,
    .sales-location-card,
    .review-card,
    .feature-box {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .cv-section,
    .teacher-profile-card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
}
