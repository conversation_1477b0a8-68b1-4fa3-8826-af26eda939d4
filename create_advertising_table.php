<?php
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== Creating advertising_banners table ===\n";

try {
    // Drop table if exists and recreate
    DB::statement("DROP TABLE IF EXISTS `advertising_banners_translations`");
    DB::statement("DROP TABLE IF EXISTS `advertising_banners`");
    
    echo "✅ Dropped existing tables\n";
    
    // Create advertising_banners table
    $sql = "
    CREATE TABLE `advertising_banners` (
        `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
        `position` enum('home1','home2','course','course_sidebar','bundle','bundle_sidebar') COLLATE utf8mb4_unicode_ci NOT NULL,
        `image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
        `size` int(10) unsigned NOT NULL DEFAULT '12',
        `link` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
        `published` tinyint(1) NOT NULL DEFAULT '0',
        `created_at` int(10) unsigned NOT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    DB::statement($sql);
    echo "✅ Created advertising_banners table\n";
    
    // Create translations table
    $sqlTranslations = "
    CREATE TABLE `advertising_banners_translations` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `advertising_banner_id` int(10) unsigned NOT NULL,
        `locale` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
        `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
        PRIMARY KEY (`id`),
        KEY `advertising_banners_translations_advertising_banner_id_foreign` (`advertising_banner_id`),
        KEY `advertising_banners_translations_locale_index` (`locale`),
        CONSTRAINT `advertising_banners_translations_advertising_banner_id_foreign` 
            FOREIGN KEY (`advertising_banner_id`) REFERENCES `advertising_banners` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    DB::statement($sqlTranslations);
    echo "✅ Created advertising_banners_translations table\n";
    
    // Test the query that was failing
    $result = DB::table('advertising_banners')
        ->where('published', true)
        ->whereIn('position', ['home1', 'home2'])
        ->get();
    
    echo "✅ Test query executed successfully!\n";
    echo "🎉 All done! advertising_banners table is ready.\n";
    
    // Mark migration as run in migrations table
    $migrationName = '2020_12_13_205751_create_advertising_banners_table';
    $migrationTransName = '2021_09_20_140241_create_advertising_banners_translations_table';
    
    DB::table('migrations')->updateOrInsert(
        ['migration' => $migrationName],
        ['migration' => $migrationName, 'batch' => 1]
    );
    
    DB::table('migrations')->updateOrInsert(
        ['migration' => $migrationTransName],
        ['migration' => $migrationTransName, 'batch' => 1]
    );
    
    echo "✅ Migration records updated\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
