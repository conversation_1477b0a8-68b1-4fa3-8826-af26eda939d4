<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WebinarReport extends Model
{
    protected $table = 'webinar_reports';
    public $timestamps = false;
    protected $dateFormat = 'U';
    protected $guarded = ['id'];

    public function user()
    {
        return $this->belongsTo('App\User', 'user_id', 'id');
    }

    public function webinar()
    {
        return $this->belongsTo('App\Models\Webinar', 'webinar_id', 'id');
    }
}
