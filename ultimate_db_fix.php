<?php
// Ultimate fix for advertising_banners table issue

echo "Starting ultimate database fix...\n";

// Database connection parameters from .env
$host = 'localhost';
$username = 'test';
$password = '@Qazqaz123';
$database = 'messultan';

try {
    // Create PDO connection
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database successfully\n";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'advertising_banners'");
    $exists = $stmt->rowCount() > 0;
    
    if ($exists) {
        echo "⚠️  Table advertising_banners already exists, dropping it...\n";
        $pdo->exec("DROP TABLE IF EXISTS advertising_banners_translations");
        $pdo->exec("DROP TABLE IF EXISTS advertising_banners");
    }
    
    // Create advertising_banners table
    $createTable = "
    CREATE TABLE advertising_banners (
        id int(10) unsigned NOT NULL AUTO_INCREMENT,
        title varchar(255) NOT NULL,
        position enum('home1','home2','course','course_sidebar','bundle','bundle_sidebar') NOT NULL,
        image varchar(255) NOT NULL,
        size int(10) unsigned NOT NULL DEFAULT 12,
        link varchar(255) NOT NULL,
        published tinyint(1) NOT NULL DEFAULT 0,
        created_at int(10) unsigned NOT NULL,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTable);
    echo "✅ Created advertising_banners table\n";
    
    // Create translations table
    $createTransTable = "
    CREATE TABLE advertising_banners_translations (
        id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        advertising_banner_id int(10) unsigned NOT NULL,
        locale varchar(255) NOT NULL,
        title varchar(255) NOT NULL,
        PRIMARY KEY (id),
        KEY advertising_banners_translations_advertising_banner_id_foreign (advertising_banner_id),
        KEY advertising_banners_translations_locale_index (locale),
        CONSTRAINT advertising_banners_translations_advertising_banner_id_foreign 
            FOREIGN KEY (advertising_banner_id) REFERENCES advertising_banners (id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTransTable);
    echo "✅ Created advertising_banners_translations table\n";
    
    // Insert sample data to prevent empty errors
    $insertSample = "
    INSERT INTO advertising_banners (id, title, position, image, size, link, published, created_at) 
    VALUES (1, 'Sample Banner', 'home1', '/assets/default/img/banner.jpg', 12, '#', 0, " . time() . ")
    ";
    
    $pdo->exec($insertSample);
    echo "✅ Inserted sample data\n";
    
    // Test the problematic query
    $stmt = $pdo->prepare("SELECT * FROM advertising_banners WHERE published = ? AND position IN (?, ?)");
    $stmt->execute([1, 'home1', 'home2']);
    $result = $stmt->fetchAll();
    
    echo "✅ Test query executed successfully\n";
    echo "Found " . count($result) . " records\n";
    
    // Update Laravel migrations table
    $checkMigration = $pdo->query("SHOW TABLES LIKE 'migrations'")->rowCount();
    if ($checkMigration > 0) {
        $pdo->exec("DELETE FROM migrations WHERE migration LIKE '%advertising_banners%'");
        $pdo->exec("INSERT INTO migrations (migration, batch) VALUES ('2020_12_13_205751_create_advertising_banners_table', 1)");
        $pdo->exec("INSERT INTO migrations (migration, batch) VALUES ('2021_09_20_140241_create_advertising_banners_translations_table', 1)");
        echo "✅ Updated migrations table\n";
    }
    
    echo "\n🎉 Database fix completed successfully!\n";
    echo "Your website should now work without errors.\n";
    
} catch (PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ General Error: " . $e->getMessage() . "\n";
}
?>
