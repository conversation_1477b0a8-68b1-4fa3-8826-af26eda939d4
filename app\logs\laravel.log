[2020-09-03 18:55:59] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36)
[stacktrace]
#0 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(91): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false)
#1 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(805): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(691): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(796): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(637): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(781): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1284): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(151): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}()
#9 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php(55): call_user_func(Object(Closure))
#10 C:\\xampp\\htdocs\\webinar\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(385): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#11 C:\\xampp\\htdocs\\webinar\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(123): Facade\\Ignition\\IgnitionServiceProvider->hasCustomViewEnginesRegistered()
#12 C:\\xampp\\htdocs\\webinar\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(82): Facade\\Ignition\\IgnitionServiceProvider->registerViewEngines()
#13 [internal function]: Facade\\Ignition\\IgnitionServiceProvider->boot()
#14 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): call_user_func_array(Array, Array)
#15 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(37): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(39): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(596): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(867): Illuminate\\Container\\Container->call(Array)
#20 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(850): Illuminate\\Foundation\\Application->bootProvider(Object(Facade\\Ignition\\IgnitionServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Facade\\Ignition\\IgnitionServiceProvider), 14)
#22 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): array_walk(Array, Object(Closure))
#23 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(151): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(135): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#27 C:\\xampp\\htdocs\\webinar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\webinar\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}
"} 
