<?php return array (
  'akcybex/laravel-jazzcash' => 
  array (
    'providers' => 
    array (
      0 => 'AKCybex\\JazzCash\\AKJazzCashServiceProvider',
    ),
    'aliases' => 
    array (
      'JazzCash' => 'AKCybex\\JazzCash\\Facades\\JazzCash',
    ),
  ),
  'anandsiddharth/laravel-paytm-wallet' => 
  array (
    'providers' => 
    array (
      0 => 'Anand\\LaravelPaytmWallet\\PaytmWalletServiceProvider',
    ),
    'aliases' => 
    array (
      'PaytmWallet' => 'Anand\\LaravelPaytmWallet\\Facades\\PaytmWallet',
    ),
  ),
  'astrotomic/laravel-translatable' => 
  array (
    'providers' => 
    array (
      0 => 'Astrotomic\\Translatable\\TranslatableServiceProvider',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
  ),
  'blade-ui-kit/blade-icons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    ),
  ),
  'carlos-meneses/laravel-mpdf' => 
  array (
    'providers' => 
    array (
      0 => 'Mccarlosen\\LaravelMpdf\\LaravelMpdfServiceProvider',
    ),
    'aliases' => 
    array (
      'PDF' => 'Mccarlosen\\LaravelMpdf\\Facades\\LaravelMpdf',
    ),
  ),
  'chapa/chapa-laravel' => 
  array (
    'aliases' => 
    array (
      'Chapa' => 'Chapa\\Chapa\\Facades\\Chapa',
    ),
    'providers' => 
    array (
      0 => 'Chapa\\Chapa\\ChapaServiceProvider',
    ),
  ),
  'craftsys/msg91-laravel' => 
  array (
    'aliases' => 
    array (
      'Msg91' => 'Craftsys\\Msg91\\Facade\\Msg91',
    ),
    'providers' => 
    array (
      0 => 'Craftsys\\Msg91\\Msg91LaravelServiceProvider',
    ),
  ),
  'cviebrock/eloquent-sluggable' => 
  array (
    'providers' => 
    array (
      0 => 'Cviebrock\\EloquentSluggable\\ServiceProvider',
    ),
  ),
  'fideloper/proxy' => 
  array (
    'providers' => 
    array (
      0 => 'Fideloper\\Proxy\\TrustedProxyServiceProvider',
    ),
  ),
  'fruitcake/laravel-cors' => 
  array (
    'providers' => 
    array (
      0 => 'Fruitcake\\Cors\\CorsServiceProvider',
    ),
  ),
  'gizemsever/laravel-paytr' => 
  array (
    'providers' => 
    array (
      0 => 'Gizemsever\\LaravelPaytr\\PaytrServiceProvider',
    ),
    'aliases' => 
    array (
      'Paytr' => 'Gizemsever\\LaravelPaytr\\PaytrFacade',
    ),
  ),
  'guysolamour/laravel-cinetpay' => 
  array (
    'providers' => 
    array (
      0 => 'Guysolamour\\Cinetpay\\ServiceProvider',
    ),
  ),
  'haggag/laravel-tap-payment' => 
  array (
    'providers' => 
    array (
      0 => 'Essam\\TapPayment\\TapServiceProvider',
    ),
  ),
  'intervention/image' => 
  array (
    'providers' => 
    array (
      0 => 'Intervention\\Image\\ImageServiceProvider',
    ),
    'aliases' => 
    array (
      'Image' => 'Intervention\\Image\\Facades\\Image',
    ),
  ),
  'jenssegers/agent' => 
  array (
    'providers' => 
    array (
      0 => 'Jenssegers\\Agent\\AgentServiceProvider',
    ),
    'aliases' => 
    array (
      'Agent' => 'Jenssegers\\Agent\\Facades\\Agent',
    ),
  ),
  'joisarjignesh/bigbluebutton' => 
  array (
    'providers' => 
    array (
      0 => 'JoisarJignesh\\Bigbluebutton\\BigbluebuttonServiceProvider',
    ),
    'aliases' => 
    array (
      'Bigbluebutton' => 'JoisarJignesh\\Bigbluebutton\\Facades\\Bigbluebutton',
    ),
  ),
  'jorenvanhocht/laravel-share' => 
  array (
    'providers' => 
    array (
      0 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    ),
    'aliases' => 
    array (
      'Share' => 'Jorenvh\\Share\\ShareFacade',
    ),
  ),
  'jubaer/zoom-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Jubaer\\Zoom\\ZoomServiceProvider',
    ),
    'aliases' => 
    array (
      'Zoom' => 'Jubaer\\Zoom\\Facades\\Zoom',
    ),
  ),
  'kavenegar/laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Kavenegar\\Laravel\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Kavenegar' => 'Kavenegar\\Laravel\\Facade',
    ),
  ),
  'kreait/laravel-firebase' => 
  array (
    'providers' => 
    array (
      0 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Firebase' => 'Kreait\\Laravel\\Firebase\\Facades\\Firebase',
    ),
  ),
  'kyrax324/laravel-ipay88' => 
  array (
    'providers' => 
    array (
      0 => 'IPay88\\IPay88ServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
  ),
  'mews/captcha' => 
  array (
    'providers' => 
    array (
      0 => 'Mews\\Captcha\\CaptchaServiceProvider',
    ),
    'aliases' => 
    array (
      'Captcha' => 'Mews\\Captcha\\Facades\\Captcha',
    ),
  ),
  'mews/purifier' => 
  array (
    'providers' => 
    array (
      0 => 'Mews\\Purifier\\PurifierServiceProvider',
    ),
    'aliases' => 
    array (
      'Purifier' => 'Mews\\Purifier\\Facades\\Purifier',
    ),
  ),
  'moemengaballah/msegat' => 
  array (
    'providers' => 
    array (
      0 => 'MoemenGaballah\\Msegat\\MsegatServiceProvider',
    ),
    'aliases' => 
    array (
      'Msegat' => 'MoemenGaballah\\Msegat\\Msegat',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'niklasravnsborg/laravel-pdf' => 
  array (
    'providers' => 
    array (
      0 => 'niklasravnsborg\\LaravelPdf\\PdfServiceProvider',
    ),
    'aliases' => 
    array (
      'PDF' => 'niklasravnsborg\\LaravelPdf\\Facades\\Pdf',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'openai-php/laravel' => 
  array (
    'providers' => 
    array (
      0 => 'OpenAI\\Laravel\\ServiceProvider',
    ),
  ),
  'pishran/zarinpal' => 
  array (
    'providers' => 
    array (
      0 => 'Pishran\\Zarinpal\\ServiceProvider',
    ),
  ),
  'saade/blade-iconsax' => 
  array (
    'providers' => 
    array (
      0 => 'Saade\\BladeIconsax\\BladeIconsaxServiceProvider',
    ),
  ),
  'sebacarrasco93/laravel-payku' => 
  array (
    'providers' => 
    array (
      0 => 'SebaCarrasco93\\LaravelPayku\\LaravelPaykuServiceProvider',
      1 => 'SebaCarrasco93\\LaravelPayku\\RouteServiceProvider',
    ),
    'aliases' => 
    array (
      'LaravelPayku' => 'SebaCarrasco93\\LaravelPayku\\Facades\\LaravelPayku',
    ),
  ),
  'simplesoftwareio/simple-qrcode' => 
  array (
    'providers' => 
    array (
      0 => 'SimpleSoftwareIO\\QrCode\\QrCodeServiceProvider',
    ),
    'aliases' => 
    array (
      'QrCode' => 'SimpleSoftwareIO\\QrCode\\Facades\\QrCode',
    ),
  ),
  'spatie/laravel-google-calendar' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\GoogleCalendar\\GoogleCalendarServiceProvider',
    ),
    'aliases' => 
    array (
      'GoogleCalendar' => 'Spatie\\GoogleCalendar\\GoogleCalendarFacade',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
  ),
  'ssheduardo/redsys-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Ssheduardo\\Redsys\\RedsysServiceProvider',
    ),
    'aliases' => 
    array (
      'Redsys' => 'Ssheduardo\\Redsys\\Facades\\Redsys',
    ),
  ),
  'stijnvanouplines/blade-country-flags' => 
  array (
    'providers' => 
    array (
      0 => 'StijnVanouplines\\BladeCountryFlags\\BladeCountryFlagsServiceProvider',
    ),
  ),
  'torann/geoip' => 
  array (
    'providers' => 
    array (
      0 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    ),
    'aliases' => 
    array (
      'GeoIP' => 'Torann\\GeoIP\\Facades\\GeoIP',
    ),
  ),
  'tymon/jwt-auth' => 
  array (
    'aliases' => 
    array (
      'JWTAuth' => 'Tymon\\JWTAuth\\Facades\\JWTAuth',
      'JWTFactory' => 'Tymon\\JWTAuth\\Facades\\JWTFactory',
    ),
    'providers' => 
    array (
      0 => 'Tymon\\JWTAuth\\Providers\\LaravelServiceProvider',
    ),
  ),
  'tzsk/payu' => 
  array (
    'providers' => 
    array (
      0 => 'Tzsk\\Payu\\PayuServiceProvider',
    ),
    'aliases' => 
    array (
      'Payu' => 'Tzsk\\Payu\\Facades\\Payu',
    ),
  ),
  'unicodeveloper/laravel-paystack' => 
  array (
    'providers' => 
    array (
      0 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    ),
    'aliases' => 
    array (
      'Paystack' => 'Unicodeveloper\\Paystack\\Facades\\Paystack',
    ),
  ),
  'unisharp/laravel-filemanager' => 
  array (
    'providers' => 
    array (
      0 => 'UniSharp\\LaravelFilemanager\\LaravelFilemanagerServiceProvider',
    ),
    'aliases' => 
    array (
    ),
  ),
  'vonage/vonage-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Vonage\\Laravel\\VonageServiceProvider',
    ),
    'aliases' => 
    array (
      'Vonage' => 'Vonage\\Laravel\\Facade\\Vonage',
    ),
  ),
  'vrajroham/laravel-bitpay' => 
  array (
    'providers' => 
    array (
      0 => 'Vrajroham\\LaravelBitpay\\LaravelBitpayServiceProvider',
    ),
    'aliases' => 
    array (
      'LaravelBitpay' => 'Vrajroham\\LaravelBitpay\\LaravelBitpayFacade',
    ),
  ),
);