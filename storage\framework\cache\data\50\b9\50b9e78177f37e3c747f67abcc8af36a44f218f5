1749100938O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:612;s:4:"slug";s:15:"sample-category";s:9:"parent_id";N;s:4:"icon";s:51:"/store/1/default_images/categories_icons/anchor.png";s:5:"order";i:1;}s:11:" * original";a:5:{s:2:"id";i:612;s:4:"slug";s:15:"sample-category";s:9:"parent_id";N;s:4:"icon";s:51:"/store/1/default_images/categories_icons/anchor.png";s:5:"order";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";s:1:"U";s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:13:"subCategories";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:20:"translatedAttributes";a:1:{i:0;s:5:"title";}s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}