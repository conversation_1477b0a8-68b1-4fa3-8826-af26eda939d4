/**
 * Sultan Educational Platform - Homepage JavaScript
 * Handles interactive elements, animations, and map integration
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize Feather Icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
    
    // Initialize AOS (Animate On Scroll) if available
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });
    }
    
    // Demo Videos Modal Handler
    initDemoVideoModals();
    
    // Course Cards Hover Effects
    initCourseCardEffects();
    
    // Sales Location Cards
    initSalesLocationEffects();
    
    // Student Reviews Swiper
    initStudentReviewsSwiper();
    
    // Why Choose Features Animation
    initFeatureBoxAnimations();
    
    // CTA Section Animations
    initCTAAnimations();
    
    // Smooth Scrolling for Anchor Links
    initSmoothScrolling();
    
    // Hero Section Parallax Effect
    initHeroParallax();
});

/**
 * Initialize Demo Video Modals
 */
function initDemoVideoModals() {
    const playButtons = document.querySelectorAll('.play-button');
    
    playButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const videoUrl = this.getAttribute('data-video-url');
            const videoTitle = this.getAttribute('data-video-title');
            
            if (videoUrl) {
                // Create and show video modal
                showVideoModal(videoUrl, videoTitle);
            }
        });
    });
}

/**
 * Show Video Modal
 */
function showVideoModal(videoUrl, title) {
    const modalHtml = `
        <div class="modal fade" id="demoVideoModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title || 'درس تجريبي'}</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="embed-responsive embed-responsive-16by9">
                            <iframe class="embed-responsive-item" src="${videoUrl}" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('demoVideoModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add new modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Show modal
    $('#demoVideoModal').modal('show');
    
    // Clean up when modal is hidden
    $('#demoVideoModal').on('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * Initialize Course Card Effects
 */
function initCourseCardEffects() {
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px)';
            this.style.boxShadow = '0 20px 60px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
        });
    });
}

/**
 * Initialize Sales Location Effects
 */
function initSalesLocationEffects() {
    const locationCards = document.querySelectorAll('.sales-location-card');
    
    locationCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 15px 40px rgba(0,0,0,0.12)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 5px 20px rgba(0,0,0,0.08)';
        });
    });
}

/**
 * Initialize Student Reviews Swiper
 */
function initStudentReviewsSwiper() {
    if (typeof Swiper !== 'undefined') {
        new Swiper('.reviews-swiper', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                },
                992: {
                    slidesPerView: 3,
                }
            }
        });
    }
}

/**
 * Initialize Feature Box Animations
 */
function initFeatureBoxAnimations() {
    const featureBoxes = document.querySelectorAll('.feature-box');
    
    // Create intersection observer for animation on scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'slideUp 0.6s ease-out forwards';
                entry.target.style.opacity = '1';
            }
        });
    }, {
        threshold: 0.1
    });
    
    featureBoxes.forEach((box, index) => {
        box.style.opacity = '0';
        box.style.animationDelay = `${index * 0.2}s`;
        observer.observe(box);
    });
}

/**
 * Initialize CTA Section Animations
 */
function initCTAAnimations() {
    const ctaButtons = document.querySelectorAll('.cta-buttons .btn');
    
    ctaButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.2)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

/**
 * Initialize Smooth Scrolling
 */
function initSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                e.preventDefault();
                
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Initialize Hero Section Parallax Effect
 */
function initHeroParallax() {
    const heroSection = document.querySelector('.sultan-hero-section');
    const heroImage = document.querySelector('.hero-image img');
    
    if (heroSection && heroImage) {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            heroImage.style.transform = `translateY(${rate}px)`;
        });
    }
}

/**
 * Initialize Google Maps for Sales Locations
 */
function initMap() {
    const salesLocations = [
        { 
            lat: 24.7136, 
            lng: 46.6753, 
            title: 'مكتبة النور - الرياض',
            address: 'شارع الملك فهد - حي العليا',
            phone: '966501234567+'
        },
        { 
            lat: 21.4858, 
            lng: 39.1925, 
            title: 'مكتبة المعرفة - جدة',
            address: 'طريق الملك عبدالعزيز - البلد',
            phone: '966507654321+'
        },
        { 
            lat: 26.4207, 
            lng: 50.0888, 
            title: 'مركز التعليم الحديث - الدمام',
            address: 'شارع الأمير محمد بن فهد',
            phone: '966509876543+'
        }
    ];
    
    const mapElement = document.getElementById('sales-locations-map');
    
    if (mapElement && typeof google !== 'undefined') {
        const map = new google.maps.Map(mapElement, {
            zoom: 6,
            center: { lat: 24.7136, lng: 46.6753 },
            styles: [
                {
                    "featureType": "all",
                    "elementType": "geometry.fill",
                    "stylers": [{"weight": "2.00"}]
                },
                {
                    "featureType": "all",
                    "elementType": "geometry.stroke",
                    "stylers": [{"color": "#9c9c9c"}]
                },
                {
                    "featureType": "all",
                    "elementType": "labels.text",
                    "stylers": [{"visibility": "on"}]
                }
            ]
        });
        
        salesLocations.forEach(location => {
            const marker = new google.maps.Marker({
                position: { lat: location.lat, lng: location.lng },
                map: map,
                title: location.title,
                animation: google.maps.Animation.DROP
            });
            
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="padding: 10px; max-width: 250px;">
                        <h6 style="margin-bottom: 8px; color: #333;">${location.title}</h6>
                        <p style="margin-bottom: 5px; color: #666; font-size: 14px;">
                            <i class="fas fa-map-marker-alt" style="margin-left: 5px;"></i>
                            ${location.address}
                        </p>
                        <p style="margin-bottom: 0; color: #666; font-size: 14px;">
                            <i class="fas fa-phone" style="margin-left: 5px;"></i>
                            ${location.phone}
                        </p>
                    </div>
                `
            });
            
            marker.addListener('click', function() {
                infoWindow.open(map, marker);
            });
        });
    } else {
        // Fallback: Show simple map placeholder
        mapElement.innerHTML = `
            <div class="d-flex align-items-center justify-content-center h-100">
                <div class="text-center">
                    <i data-feather="map" width="48" height="48" class="text-muted mb-3"></i>
                    <p class="text-muted">خريطة أماكن بيع الأكواد</p>
                    <small class="text-muted">الخريطة التفاعلية متوفرة عند تحميل Google Maps</small>
                </div>
            </div>
        `;
        
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    }
}

/**
 * Load Google Maps Script
 */
function loadGoogleMaps() {
    const script = document.createElement('script');
    script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap';
    script.defer = true;
    script.async = true;
    document.head.appendChild(script);
}

/**
 * Initialize Map Button Handler
 */
document.addEventListener('DOMContentLoaded', function() {
    const mapButton = document.querySelector('button[onclick="initMap()"]');
    
    if (mapButton) {
        mapButton.addEventListener('click', function() {
            this.innerHTML = '<span class="spinner-border spinner-border-sm ml-2" role="status"></span> جاري التحميل...';
            this.disabled = true;
            
            // Simulate loading Google Maps
            setTimeout(() => {
                initMap();
                this.style.display = 'none';
            }, 2000);
        });
    }
});

/**
 * Utility Functions
 */

// Format numbers with Arabic locale
function formatArabicNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
}

// Animate counter numbers
function animateCounter(element, target, duration = 2000) {
    let start = 0;
    const increment = target / (duration / 16);
    
    const timer = setInterval(() => {
        start += increment;
        element.textContent = Math.floor(start);
        
        if (start >= target) {
            clearInterval(timer);
            element.textContent = target;
        }
    }, 16);
}

// Show notification toast
function showNotification(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} toast-notification`;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i data-feather="${type === 'success' ? 'check-circle' : 'alert-circle'}" width="20" height="20" class="ml-2"></i>
            ${message}
            <button type="button" class="close mr-auto" onclick="this.parentElement.parentElement.remove()">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
    
    // Animate in
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => toast.remove(), 300);
    }, 5000);
}

// Export functions for global access
window.initMap = initMap;
window.loadGoogleMaps = loadGoogleMaps;
window.showNotification = showNotification;
