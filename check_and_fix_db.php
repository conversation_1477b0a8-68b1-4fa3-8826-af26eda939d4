<?php
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

try {
    echo "=== Database Connection Test ===\n";
    DB::connection()->getPdo();
    echo "✅ Database connected successfully!\n\n";
    
    echo "=== Checking Tables ===\n";
    
    // Check advertising_banners table
    if (Schema::hasTable('advertising_banners')) {
        echo "✅ Table 'advertising_banners' EXISTS\n";
        
        // Check table structure
        $columns = Schema::getColumnListing('advertising_banners');
        echo "   Columns: " . implode(', ', $columns) . "\n";
        
        // Check data count
        $count = DB::table('advertising_banners')->count();
        echo "   Records count: $count\n";
        
    } else {
        echo "❌ Table 'advertising_banners' MISSING\n";
        echo "   Creating table now...\n";
        
        // Create the table manually
        DB::statement("
            CREATE TABLE `advertising_banners` (
                `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `position` enum('home1','home2','course','course_sidebar','bundle','bundle_sidebar') COLLATE utf8mb4_unicode_ci NOT NULL,
                `image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `size` int(10) unsigned NOT NULL DEFAULT '12',
                `link` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `published` tinyint(1) NOT NULL DEFAULT '0',
                `created_at` int(10) unsigned NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        echo "   ✅ Table 'advertising_banners' created successfully!\n";
    }
    
    // Check translations table
    if (Schema::hasTable('advertising_banners_translations')) {
        echo "✅ Table 'advertising_banners_translations' EXISTS\n";
    } else {
        echo "❌ Table 'advertising_banners_translations' MISSING\n";
        echo "   Creating translations table...\n";
        
        DB::statement("
            CREATE TABLE `advertising_banners_translations` (
                `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                `advertising_banner_id` int(10) unsigned NOT NULL,
                `locale` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                PRIMARY KEY (`id`),
                KEY `advertising_banners_translations_advertising_banner_id_foreign` (`advertising_banner_id`),
                KEY `advertising_banners_translations_locale_index` (`locale`),
                CONSTRAINT `advertising_banners_translations_advertising_banner_id_foreign` 
                    FOREIGN KEY (`advertising_banner_id`) REFERENCES `advertising_banners` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        echo "   ✅ Table 'advertising_banners_translations' created successfully!\n";
    }
    
    echo "\n=== All Tables in Database ===\n";
    $tables = DB::select('SHOW TABLES');
    foreach ($tables as $table) {
        $tableName = array_values((array) $table)[0];
        if (strpos($tableName, 'advertising') !== false) {
            echo "📋 $tableName\n";
        }
    }
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "You can now access your website without errors.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Database connection failed. Please check your .env settings.\n";
}
?>
