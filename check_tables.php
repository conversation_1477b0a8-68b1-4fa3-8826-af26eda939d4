<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Check if advertising_banners table exists
if (Schema::hasTable('advertising_banners')) {
    echo "Table 'advertising_banners' exists.\n";
} else {
    echo "Table 'advertising_banners' does not exist.\n";
    echo "Running migration...\n";
    
    // Run the specific migration
    try {
        Artisan::call('migrate', [
            '--path' => 'database/migrations/2020_12_13_205751_create_advertising_banners_table.php',
            '--force' => true
        ]);
        echo "Migration completed successfully.\n";
    } catch (Exception $e) {
        echo "Migration failed: " . $e->getMessage() . "\n";
    }
}

// List all tables
$tables = DB::select('SHOW TABLES');
echo "\nAll tables in database:\n";
foreach ($tables as $table) {
    $tableName = array_values((array) $table)[0];
    echo "- " . $tableName . "\n";
}
