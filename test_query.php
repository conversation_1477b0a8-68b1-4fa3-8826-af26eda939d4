<?php
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    echo "Testing the problematic query...\n";
    
    $advertisingBanners = DB::table('advertising_banners')
        ->where('published', true)
        ->whereIn('position', ['home1', 'home2'])
        ->get();
    
    echo "✅ Query executed successfully!\n";
    echo "Found " . $advertisingBanners->count() . " advertising banners.\n";
    echo "🎉 The advertising_banners table is working correctly!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
