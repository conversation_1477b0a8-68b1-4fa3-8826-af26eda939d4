<?php return array (
  'providers' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    2 => 'Illuminate\\Bus\\BusServiceProvider',
    3 => 'Illuminate\\Cache\\CacheServiceProvider',
    4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    5 => 'Illuminate\\Cookie\\CookieServiceProvider',
    6 => 'Illuminate\\Database\\DatabaseServiceProvider',
    7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    10 => 'Illuminate\\Hashing\\HashServiceProvider',
    11 => 'Illuminate\\Mail\\MailServiceProvider',
    12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    15 => 'Illuminate\\Queue\\QueueServiceProvider',
    16 => 'Illuminate\\Redis\\RedisServiceProvider',
    17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    18 => 'Illuminate\\Session\\SessionServiceProvider',
    19 => 'Illuminate\\Translation\\TranslationServiceProvider',
    20 => 'Illuminate\\Validation\\ValidationServiceProvider',
    21 => 'Illuminate\\View\\ViewServiceProvider',
    22 => 'AKCybex\\JazzCash\\AKJazzCashServiceProvider',
    23 => 'Anand\\LaravelPaytmWallet\\PaytmWalletServiceProvider',
    24 => 'Astrotomic\\Translatable\\TranslatableServiceProvider',
    25 => 'Barryvdh\\Debugbar\\ServiceProvider',
    26 => 'Barryvdh\\DomPDF\\ServiceProvider',
    27 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    28 => 'Mccarlosen\\LaravelMpdf\\LaravelMpdfServiceProvider',
    29 => 'Chapa\\Chapa\\ChapaServiceProvider',
    30 => 'Craftsys\\Msg91\\Msg91LaravelServiceProvider',
    31 => 'Cviebrock\\EloquentSluggable\\ServiceProvider',
    32 => 'Fideloper\\Proxy\\TrustedProxyServiceProvider',
    33 => 'Fruitcake\\Cors\\CorsServiceProvider',
    34 => 'Gizemsever\\LaravelPaytr\\PaytrServiceProvider',
    35 => 'Guysolamour\\Cinetpay\\ServiceProvider',
    36 => 'Essam\\TapPayment\\TapServiceProvider',
    37 => 'Intervention\\Image\\ImageServiceProvider',
    38 => 'Jenssegers\\Agent\\AgentServiceProvider',
    39 => 'JoisarJignesh\\Bigbluebutton\\BigbluebuttonServiceProvider',
    40 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    41 => 'Jubaer\\Zoom\\ZoomServiceProvider',
    42 => 'Kavenegar\\Laravel\\ServiceProvider',
    43 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    44 => 'IPay88\\IPay88ServiceProvider',
    45 => 'Laravel\\Sail\\SailServiceProvider',
    46 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    47 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    48 => 'Laravel\\Tinker\\TinkerServiceProvider',
    49 => 'Laravel\\Ui\\UiServiceProvider',
    50 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    51 => 'Mews\\Captcha\\CaptchaServiceProvider',
    52 => 'Mews\\Purifier\\PurifierServiceProvider',
    53 => 'MoemenGaballah\\Msegat\\MsegatServiceProvider',
    54 => 'Carbon\\Laravel\\ServiceProvider',
    55 => 'niklasravnsborg\\LaravelPdf\\PdfServiceProvider',
    56 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    57 => 'Termwind\\Laravel\\TermwindServiceProvider',
    58 => 'OpenAI\\Laravel\\ServiceProvider',
    59 => 'Pishran\\Zarinpal\\ServiceProvider',
    60 => 'Saade\\BladeIconsax\\BladeIconsaxServiceProvider',
    61 => 'SebaCarrasco93\\LaravelPayku\\LaravelPaykuServiceProvider',
    62 => 'SebaCarrasco93\\LaravelPayku\\RouteServiceProvider',
    63 => 'SimpleSoftwareIO\\QrCode\\QrCodeServiceProvider',
    64 => 'Spatie\\GoogleCalendar\\GoogleCalendarServiceProvider',
    65 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    66 => 'Ssheduardo\\Redsys\\RedsysServiceProvider',
    67 => 'StijnVanouplines\\BladeCountryFlags\\BladeCountryFlagsServiceProvider',
    68 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    69 => 'Tymon\\JWTAuth\\Providers\\LaravelServiceProvider',
    70 => 'Tzsk\\Payu\\PayuServiceProvider',
    71 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    72 => 'UniSharp\\LaravelFilemanager\\LaravelFilemanagerServiceProvider',
    73 => 'Vonage\\Laravel\\VonageServiceProvider',
    74 => 'Vrajroham\\LaravelBitpay\\LaravelBitpayServiceProvider',
    75 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    76 => 'Cviebrock\\EloquentSluggable\\ServiceProvider',
    77 => 'Barryvdh\\Debugbar\\ServiceProvider',
    78 => 'App\\Providers\\AppServiceProvider',
    79 => 'App\\Providers\\AuthServiceProvider',
    80 => 'App\\Providers\\EventServiceProvider',
    81 => 'App\\Providers\\RouteServiceProvider',
    82 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    83 => 'Mews\\Captcha\\CaptchaServiceProvider',
    84 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    85 => 'App\\Providers\\MinioStorageServiceProvider',
    86 => 'Ssheduardo\\Redsys\\RedsysServiceProvider',
    87 => 'Jubaer\\Zoom\\ZoomServiceProvider',
    88 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    89 => 'niklasravnsborg\\LaravelPdf\\PdfServiceProvider',
    90 => 'Kavenegar\\Laravel\\ServiceProvider',
    91 => 'MoemenGaballah\\Msegat\\MsegatServiceProvider',
    92 => 'Craftsys\\Msg91\\Msg91LaravelServiceProvider',
    93 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    94 => 'Paytabscom\\Laravel_paytabs\\PaypageServiceProvider',
    95 => 'PayMob\\PayMobServiceProvider',
    96 => 'Clickpaysa\\Laravel_package\\PaypageServiceProvider',
  ),
  'eager' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Cookie\\CookieServiceProvider',
    2 => 'Illuminate\\Database\\DatabaseServiceProvider',
    3 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    4 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    5 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    6 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    7 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    8 => 'Illuminate\\Session\\SessionServiceProvider',
    9 => 'Illuminate\\View\\ViewServiceProvider',
    10 => 'AKCybex\\JazzCash\\AKJazzCashServiceProvider',
    11 => 'Anand\\LaravelPaytmWallet\\PaytmWalletServiceProvider',
    12 => 'Astrotomic\\Translatable\\TranslatableServiceProvider',
    13 => 'Barryvdh\\Debugbar\\ServiceProvider',
    14 => 'Barryvdh\\DomPDF\\ServiceProvider',
    15 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    16 => 'Mccarlosen\\LaravelMpdf\\LaravelMpdfServiceProvider',
    17 => 'Chapa\\Chapa\\ChapaServiceProvider',
    18 => 'Craftsys\\Msg91\\Msg91LaravelServiceProvider',
    19 => 'Cviebrock\\EloquentSluggable\\ServiceProvider',
    20 => 'Fideloper\\Proxy\\TrustedProxyServiceProvider',
    21 => 'Fruitcake\\Cors\\CorsServiceProvider',
    22 => 'Gizemsever\\LaravelPaytr\\PaytrServiceProvider',
    23 => 'Guysolamour\\Cinetpay\\ServiceProvider',
    24 => 'Essam\\TapPayment\\TapServiceProvider',
    25 => 'Intervention\\Image\\ImageServiceProvider',
    26 => 'Jenssegers\\Agent\\AgentServiceProvider',
    27 => 'JoisarJignesh\\Bigbluebutton\\BigbluebuttonServiceProvider',
    28 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    29 => 'Jubaer\\Zoom\\ZoomServiceProvider',
    30 => 'Kavenegar\\Laravel\\ServiceProvider',
    31 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    32 => 'IPay88\\IPay88ServiceProvider',
    33 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    34 => 'Laravel\\Ui\\UiServiceProvider',
    35 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    36 => 'Mews\\Captcha\\CaptchaServiceProvider',
    37 => 'Mews\\Purifier\\PurifierServiceProvider',
    38 => 'MoemenGaballah\\Msegat\\MsegatServiceProvider',
    39 => 'Carbon\\Laravel\\ServiceProvider',
    40 => 'niklasravnsborg\\LaravelPdf\\PdfServiceProvider',
    41 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    42 => 'Termwind\\Laravel\\TermwindServiceProvider',
    43 => 'Pishran\\Zarinpal\\ServiceProvider',
    44 => 'Saade\\BladeIconsax\\BladeIconsaxServiceProvider',
    45 => 'SebaCarrasco93\\LaravelPayku\\LaravelPaykuServiceProvider',
    46 => 'SebaCarrasco93\\LaravelPayku\\RouteServiceProvider',
    47 => 'SimpleSoftwareIO\\QrCode\\QrCodeServiceProvider',
    48 => 'Spatie\\GoogleCalendar\\GoogleCalendarServiceProvider',
    49 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    50 => 'Ssheduardo\\Redsys\\RedsysServiceProvider',
    51 => 'StijnVanouplines\\BladeCountryFlags\\BladeCountryFlagsServiceProvider',
    52 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    53 => 'Tymon\\JWTAuth\\Providers\\LaravelServiceProvider',
    54 => 'Tzsk\\Payu\\PayuServiceProvider',
    55 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    56 => 'UniSharp\\LaravelFilemanager\\LaravelFilemanagerServiceProvider',
    57 => 'Vonage\\Laravel\\VonageServiceProvider',
    58 => 'Vrajroham\\LaravelBitpay\\LaravelBitpayServiceProvider',
    59 => 'Cviebrock\\EloquentSluggable\\ServiceProvider',
    60 => 'Barryvdh\\Debugbar\\ServiceProvider',
    61 => 'App\\Providers\\AppServiceProvider',
    62 => 'App\\Providers\\AuthServiceProvider',
    63 => 'App\\Providers\\EventServiceProvider',
    64 => 'App\\Providers\\RouteServiceProvider',
    65 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    66 => 'Mews\\Captcha\\CaptchaServiceProvider',
    67 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    68 => 'App\\Providers\\MinioStorageServiceProvider',
    69 => 'Ssheduardo\\Redsys\\RedsysServiceProvider',
    70 => 'Jubaer\\Zoom\\ZoomServiceProvider',
    71 => 'Torann\\GeoIP\\GeoIPServiceProvider',
    72 => 'niklasravnsborg\\LaravelPdf\\PdfServiceProvider',
    73 => 'Kavenegar\\Laravel\\ServiceProvider',
    74 => 'MoemenGaballah\\Msegat\\MsegatServiceProvider',
    75 => 'Craftsys\\Msg91\\Msg91LaravelServiceProvider',
    76 => 'Kreait\\Laravel\\Firebase\\ServiceProvider',
    77 => 'Paytabscom\\Laravel_paytabs\\PaypageServiceProvider',
    78 => 'PayMob\\PayMobServiceProvider',
    79 => 'Clickpaysa\\Laravel_package\\PaypageServiceProvider',
  ),
  'deferred' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastManager' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Factory' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Broadcaster' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\QueueingDispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\BatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\DatabaseBatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'cache' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.store' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.psr6' => 'Illuminate\\Cache\\CacheServiceProvider',
    'memcached.connector' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Cache\\RateLimiter' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Foundation\\Console\\AboutCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\ClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\ForgetCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ClearCompiledCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Auth\\Console\\ClearResetsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\DbCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\MonitorCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\PruneCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\ShowCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\TableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\WipeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\DownCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentDecryptCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentEncryptCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\KeyGenerateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\OptimizeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\OptimizeClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\PackageDiscoverCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ListFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\FlushFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ForgetFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ListenCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\MonitorCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\PruneBatchesCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\PruneFailedJobsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RestartCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RetryCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RetryBatchCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\WorkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\DumpCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Seeds\\SeedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleFinishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleRunCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleClearCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleTestCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleWorkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ShowModelCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\StorageLinkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\UpCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ViewCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ViewClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\CacheTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\CastMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ChannelMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ComponentMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConsoleMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Routing\\Console\\ControllerMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\DocsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventGenerateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ExceptionMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Factories\\FactoryMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\JobMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ListenerMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\MailMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Routing\\Console\\MiddlewareMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ModelMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\NotificationMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Notifications\\Console\\NotificationTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ObserverMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\PolicyMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ProviderMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\FailedTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\TableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\BatchesTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RequestMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ResourceMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RuleMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ScopeMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Seeds\\SeederMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Session\\Console\\SessionTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ServeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\StubPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\TestMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\VendorPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migrator' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migration.repository' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migration.creator' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\FreshCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\InstallCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RefreshCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\ResetCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RollbackCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\StatusCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'composer' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'hash' => 'Illuminate\\Hashing\\HashServiceProvider',
    'hash.driver' => 'Illuminate\\Hashing\\HashServiceProvider',
    'mail.manager' => 'Illuminate\\Mail\\MailServiceProvider',
    'mailer' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Mail\\Markdown' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Contracts\\Pipeline\\Hub' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'queue' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.connection' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.failer' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.listener' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.worker' => 'Illuminate\\Queue\\QueueServiceProvider',
    'redis' => 'Illuminate\\Redis\\RedisServiceProvider',
    'redis.connection' => 'Illuminate\\Redis\\RedisServiceProvider',
    'auth.password' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'auth.password.broker' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'translator' => 'Illuminate\\Translation\\TranslationServiceProvider',
    'translation.loader' => 'Illuminate\\Translation\\TranslationServiceProvider',
    'validator' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'validation.presence' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'Laravel\\Sail\\Console\\InstallCommand' => 'Laravel\\Sail\\SailServiceProvider',
    'Laravel\\Sail\\Console\\PublishCommand' => 'Laravel\\Sail\\SailServiceProvider',
    'Laravel\\Socialite\\Contracts\\Factory' => 'Laravel\\Socialite\\SocialiteServiceProvider',
    'command.tinker' => 'Laravel\\Tinker\\TinkerServiceProvider',
    'OpenAI\\Client' => 'OpenAI\\Laravel\\ServiceProvider',
    'OpenAI\\Contracts\\ClientContract' => 'OpenAI\\Laravel\\ServiceProvider',
    'openai' => 'OpenAI\\Laravel\\ServiceProvider',
  ),
  'when' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastServiceProvider' => 
    array (
    ),
    'Illuminate\\Bus\\BusServiceProvider' => 
    array (
    ),
    'Illuminate\\Cache\\CacheServiceProvider' => 
    array (
    ),
    'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider' => 
    array (
    ),
    'Illuminate\\Hashing\\HashServiceProvider' => 
    array (
    ),
    'Illuminate\\Mail\\MailServiceProvider' => 
    array (
    ),
    'Illuminate\\Pipeline\\PipelineServiceProvider' => 
    array (
    ),
    'Illuminate\\Queue\\QueueServiceProvider' => 
    array (
    ),
    'Illuminate\\Redis\\RedisServiceProvider' => 
    array (
    ),
    'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider' => 
    array (
    ),
    'Illuminate\\Translation\\TranslationServiceProvider' => 
    array (
    ),
    'Illuminate\\Validation\\ValidationServiceProvider' => 
    array (
    ),
    'Laravel\\Sail\\SailServiceProvider' => 
    array (
    ),
    'Laravel\\Socialite\\SocialiteServiceProvider' => 
    array (
    ),
    'Laravel\\Tinker\\TinkerServiceProvider' => 
    array (
    ),
    'OpenAI\\Laravel\\ServiceProvider' => 
    array (
    ),
  ),
);