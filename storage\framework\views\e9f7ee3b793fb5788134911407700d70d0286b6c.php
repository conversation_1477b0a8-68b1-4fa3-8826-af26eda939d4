
<section class="sultan-hero-section position-relative">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6 order-2 order-lg-1">
                <div class="hero-content">
                    <h1 class="hero-title font-weight-bold text-primary mb-4">
                        منصة السلطان التعليمية
                    </h1>
                    <p class="hero-description text-gray font-16 mb-4">
                        وجهتك الأولى لتعلّم المواد الدراسية بأسلوب سهل واحترافي، بإشراف مباشر من الأستاذ المتخصص.
                    </p>
                    <div class="hero-buttons">
                        <a href="/classes" class="btn btn-primary btn-lg mr-3">
                            <i data-feather="play-circle" width="20" height="20" class="mr-2"></i>
                            ابدأ الآن
                        </a>
                        <a href="#demo-videos" class="btn btn-outline-primary btn-lg">
                            <i data-feather="video" width="20" height="20" class="mr-2"></i>
                            شاهد الدروس التجريبية
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 order-1 order-lg-2 text-center">                <div class="hero-image">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face" 
                         alt="الأستاذ السلطان" 
                         class="img-fluid rounded-circle shadow-lg"
                         style="max-width: 400px; width: 100%;">
                </div>
            </div>
        </div>
    </div>
    
    
    <div class="hero-decorations">
        <div class="decoration-1"></div>
        <div class="decoration-2"></div>
        <div class="decoration-3"></div>
    </div>
</section>


<section id="demo-videos" class="home-sections container py-5">
    <div class="text-center mb-5">
        <h2 class="section-title">فيديوهات تجريبية</h2>
        <p class="section-hint">شاهد نماذج من دروسنا التفاعلية</p>
    </div>
    
    <div class="row">
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="demo-video-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="video-thumbnail position-relative">
                    <img src="https://images.unsplash.com/photo-1509062522246-3755977927d7?w=350&h=200&fit=crop" class="img-cover w-100" alt="درس تجريبي 1" style="height: 200px;">
                    <div class="play-button position-absolute">
                        <i data-feather="play" width="30" height="30" class="text-white"></i>
                    </div>
                </div>
                <div class="card-body p-3">
                    <h5 class="card-title font-weight-bold">درس النحو الأساسي</h5>
                    <p class="card-text text-gray font-14">مقدمة شاملة في قواعد النحو العربي</p>
                    <span class="badge badge-primary">15 دقيقة</span>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="demo-video-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="video-thumbnail position-relative">
                    <img src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=350&h=200&fit=crop" class="img-cover w-100" alt="درس تجريبي 2" style="height: 200px;">
                    <div class="play-button position-absolute">
                        <i data-feather="play" width="30" height="30" class="text-white"></i>
                    </div>
                </div>
                <div class="card-body p-3">
                    <h5 class="card-title font-weight-bold">الأدب الجاهلي</h5>
                    <p class="card-text text-gray font-14">رحلة في عصر الأدب الجاهلي</p>
                    <span class="badge badge-primary">20 دقيقة</span>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="demo-video-card bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="video-thumbnail position-relative">
                    <img src="https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?w=350&h=200&fit=crop" class="img-cover w-100" alt="درس تجريبي 3" style="height: 200px;">
                    <div class="play-button position-absolute">
                        <i data-feather="play" width="30" height="30" class="text-white"></i>
                    </div>
                </div>
                <div class="card-body p-3">
                    <h5 class="card-title font-weight-bold">البلاغة والبيان</h5>
                    <p class="card-text text-gray font-14">فن البلاغة في اللغة العربية</p>
                    <span class="badge badge-primary">18 دقيقة</span>
                </div>
            </div>
        </div>
    </div>
</section>


<section class="home-sections py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <img src="/assets/default/img/sultan/why-choose.png" 
                     alt="لماذا تختار منصة السلطان" 
                     class="img-fluid rounded-lg shadow">
            </div>
            <div class="col-lg-6">
                <div class="content-area">
                    <h2 class="section-title mb-4">لماذا تختار منصة السلطان؟</h2>
                    <p class="section-description text-gray font-16 mb-4">
                        نحن نقدم محتوى تعليميًا موثوقًا، بإشراف نخبة من الأساتذة المتخصصين، 
                        مع دعم مباشر وتجربة استخدام سهلة ومحفزة للطالب.
                    </p>
                    
                    <div class="features-list">
                        <div class="feature-item d-flex align-items-center mb-3">
                            <div class="feature-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                <i data-feather="award" width="20" height="20"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 font-weight-bold">أساتذة متخصصون</h6>
                                <p class="mb-0 text-gray font-14">نخبة من أفضل المدرسين</p>
                            </div>
                        </div>
                        
                        <div class="feature-item d-flex align-items-center mb-3">
                            <div class="feature-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                <i data-feather="clock" width="20" height="20"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 font-weight-bold">مرونة في التعلم</h6>
                                <p class="mb-0 text-gray font-14">تعلم في أي وقت ومن أي مكان</p>
                            </div>
                        </div>
                        
                        <div class="feature-item d-flex align-items-center">
                            <div class="feature-icon bg-info text-white rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                <i data-feather="users" width="20" height="20"></i>
                            </div>
                            <div>
                                <h6 class="mb-1 font-weight-bold">دعم مباشر</h6>
                                <p class="mb-0 text-gray font-14">تواصل مباشر مع الأساتذة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<style>
.sultan-hero-section {
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1) 0%, rgba(var(--secondary-rgb), 0.1) 100%);
    min-height: 80vh;
    position: relative;
    overflow: hidden;
}

.hero-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.decoration-1, .decoration-2, .decoration-3 {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(var(--primary-rgb), 0.1), rgba(var(--secondary-rgb), 0.1));
}

.decoration-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation: float 6s ease-in-out infinite;
}

.decoration-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 5%;
    animation: float 8s ease-in-out infinite reverse;
}

.decoration-3 {
    width: 100px;
    height: 100px;
    top: 50%;
    right: 20%;
    animation: float 10s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.demo-video-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.demo-video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.video-thumbnail {
    cursor: pointer;
}

.play-button {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(var(--primary-rgb), 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: rgba(var(--primary-rgb), 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.feature-item {
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(10px);
}

.min-vh-75 {
    min-height: 75vh;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .decoration-1, .decoration-2, .decoration-3 {
        display: none;
    }
}
</style>
<?php /**PATH C:\xampp\htdocs\mansultan\resources\views/web/default/pages/includes/sultan_hero_sections.blade.php ENDPATH**/ ?>