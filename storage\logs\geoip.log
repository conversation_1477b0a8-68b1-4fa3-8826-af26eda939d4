[2025-06-04T05:19:29.691971+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(43): Illuminate\Support\Facades\Facade::__callStatic('getLocation', Array) #4 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(27): App\Http\Middleware\CheckRestriction->checkIpRestriction(Object(App\Models\IpRestriction), '::1') #5 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckRestriction->handle(Object(Illuminate\Http\Request), Object(Closure)) #6 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMaintenance.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #7 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #8 C:\xampp\htdocs\mansultan\app\Http\Middleware\Share.php(90): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #9 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Share->handle(Object(Illuminate\Http\Request), Object(Closure)) #10 C:\xampp\htdocs\mansultan\app\Http\Middleware\Impersonate.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Impersonate->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMobileApp.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMobileApp->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\mansultan\app\Http\Middleware\DebugBar.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\DebugBar->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\mansultan\app\Http\Middleware\SessionValidity.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SessionValidity->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\mansultan\app\Http\Middleware\UserLocale.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\UserLocale->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #24 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #25 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #26 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #27 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #28 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #33 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(799): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #35 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #37 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #40 C:\xampp\htdocs\mansultan\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #44 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\mansultan\vendor\fruitcake\laravel-cors\src\HandleCors.php(38): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #56 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #58 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #59 C:\xampp\htdocs\mansultan\public\index.php(52): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #60 {main} [] []
[2025-06-04T05:19:39.233372+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(43): Illuminate\Support\Facades\Facade::__callStatic('getLocation', Array) #4 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(27): App\Http\Middleware\CheckRestriction->checkIpRestriction(Object(App\Models\IpRestriction), '::1') #5 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckRestriction->handle(Object(Illuminate\Http\Request), Object(Closure)) #6 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMaintenance.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #7 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #8 C:\xampp\htdocs\mansultan\app\Http\Middleware\Share.php(90): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #9 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Share->handle(Object(Illuminate\Http\Request), Object(Closure)) #10 C:\xampp\htdocs\mansultan\app\Http\Middleware\Impersonate.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Impersonate->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMobileApp.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMobileApp->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\mansultan\app\Http\Middleware\DebugBar.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\DebugBar->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\mansultan\app\Http\Middleware\SessionValidity.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SessionValidity->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\mansultan\app\Http\Middleware\UserLocale.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\UserLocale->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #24 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #25 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #26 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #27 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #28 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #33 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(799): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #35 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #37 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #40 C:\xampp\htdocs\mansultan\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #44 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\mansultan\vendor\fruitcake\laravel-cors\src\HandleCors.php(38): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #56 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #58 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #59 C:\xampp\htdocs\mansultan\public\index.php(52): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #60 {main} [] []
[2025-06-04T05:20:53.170233+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(43): Illuminate\Support\Facades\Facade::__callStatic('getLocation', Array) #4 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(27): App\Http\Middleware\CheckRestriction->checkIpRestriction(Object(App\Models\IpRestriction), '::1') #5 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckRestriction->handle(Object(Illuminate\Http\Request), Object(Closure)) #6 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMaintenance.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #7 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #8 C:\xampp\htdocs\mansultan\app\Http\Middleware\Share.php(90): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #9 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Share->handle(Object(Illuminate\Http\Request), Object(Closure)) #10 C:\xampp\htdocs\mansultan\app\Http\Middleware\Impersonate.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Impersonate->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMobileApp.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMobileApp->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\mansultan\app\Http\Middleware\DebugBar.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\DebugBar->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\mansultan\app\Http\Middleware\SessionValidity.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SessionValidity->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\mansultan\app\Http\Middleware\UserLocale.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\UserLocale->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #24 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #25 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #26 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #27 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #28 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #33 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(799): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #35 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #37 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #40 C:\xampp\htdocs\mansultan\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #44 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\mansultan\vendor\fruitcake\laravel-cors\src\HandleCors.php(38): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #56 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #58 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #59 C:\xampp\htdocs\mansultan\public\index.php(52): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #60 {main} [] []
[2025-06-04T05:21:40.413829+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(43): Illuminate\Support\Facades\Facade::__callStatic('getLocation', Array) #4 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(27): App\Http\Middleware\CheckRestriction->checkIpRestriction(Object(App\Models\IpRestriction), '::1') #5 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckRestriction->handle(Object(Illuminate\Http\Request), Object(Closure)) #6 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMaintenance.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #7 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #8 C:\xampp\htdocs\mansultan\app\Http\Middleware\Share.php(90): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #9 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Share->handle(Object(Illuminate\Http\Request), Object(Closure)) #10 C:\xampp\htdocs\mansultan\app\Http\Middleware\Impersonate.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Impersonate->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMobileApp.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMobileApp->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\mansultan\app\Http\Middleware\DebugBar.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\DebugBar->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\mansultan\app\Http\Middleware\SessionValidity.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SessionValidity->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\mansultan\app\Http\Middleware\UserLocale.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\UserLocale->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #24 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #25 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #26 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #27 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #28 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #33 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(799): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #35 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #37 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #40 C:\xampp\htdocs\mansultan\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #44 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\mansultan\vendor\fruitcake\laravel-cors\src\HandleCors.php(38): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #56 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #58 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #59 C:\xampp\htdocs\mansultan\public\index.php(52): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #60 {main} [] []
[2025-06-04T05:22:18.791661+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(43): Illuminate\Support\Facades\Facade::__callStatic('getLocation', Array) #4 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(27): App\Http\Middleware\CheckRestriction->checkIpRestriction(Object(App\Models\IpRestriction), '::1') #5 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckRestriction->handle(Object(Illuminate\Http\Request), Object(Closure)) #6 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMaintenance.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #7 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #8 C:\xampp\htdocs\mansultan\app\Http\Middleware\Share.php(90): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #9 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Share->handle(Object(Illuminate\Http\Request), Object(Closure)) #10 C:\xampp\htdocs\mansultan\app\Http\Middleware\Impersonate.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Impersonate->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMobileApp.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMobileApp->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\mansultan\app\Http\Middleware\DebugBar.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\DebugBar->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\mansultan\app\Http\Middleware\SessionValidity.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SessionValidity->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\mansultan\app\Http\Middleware\UserLocale.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\UserLocale->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #24 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #25 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #26 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #27 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #28 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #33 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(799): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #35 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #37 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #40 C:\xampp\htdocs\mansultan\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #44 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\mansultan\vendor\fruitcake\laravel-cors\src\HandleCors.php(38): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #56 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #58 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #59 C:\xampp\htdocs\mansultan\public\index.php(52): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #60 {main} [] []
[2025-06-04T05:22:35.220852+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(43): Illuminate\Support\Facades\Facade::__callStatic('getLocation', Array) #4 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(27): App\Http\Middleware\CheckRestriction->checkIpRestriction(Object(App\Models\IpRestriction), '::1') #5 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckRestriction->handle(Object(Illuminate\Http\Request), Object(Closure)) #6 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMaintenance.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #7 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #8 C:\xampp\htdocs\mansultan\app\Http\Middleware\Share.php(90): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #9 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Share->handle(Object(Illuminate\Http\Request), Object(Closure)) #10 C:\xampp\htdocs\mansultan\app\Http\Middleware\Impersonate.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Impersonate->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMobileApp.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMobileApp->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\mansultan\app\Http\Middleware\DebugBar.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\DebugBar->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\mansultan\app\Http\Middleware\SessionValidity.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SessionValidity->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\mansultan\app\Http\Middleware\UserLocale.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\UserLocale->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #24 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #25 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #26 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #27 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #28 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #33 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(799): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #35 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #37 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #40 C:\xampp\htdocs\mansultan\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #44 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\mansultan\vendor\fruitcake\laravel-cors\src\HandleCors.php(38): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #56 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #58 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #59 C:\xampp\htdocs\mansultan\public\index.php(52): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #60 {main} [] []
[2025-06-04T05:26:50.163338+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(43): Illuminate\Support\Facades\Facade::__callStatic('getLocation', Array) #4 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(27): App\Http\Middleware\CheckRestriction->checkIpRestriction(Object(App\Models\IpRestriction), '::1') #5 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckRestriction->handle(Object(Illuminate\Http\Request), Object(Closure)) #6 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMaintenance.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #7 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #8 C:\xampp\htdocs\mansultan\app\Http\Middleware\Share.php(90): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #9 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Share->handle(Object(Illuminate\Http\Request), Object(Closure)) #10 C:\xampp\htdocs\mansultan\app\Http\Middleware\Impersonate.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Impersonate->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMobileApp.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMobileApp->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\mansultan\app\Http\Middleware\DebugBar.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\DebugBar->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\mansultan\app\Http\Middleware\SessionValidity.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SessionValidity->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\mansultan\app\Http\Middleware\UserLocale.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\UserLocale->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #24 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #25 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #26 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #27 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #28 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #33 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(799): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #35 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #37 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #40 C:\xampp\htdocs\mansultan\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #44 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\mansultan\vendor\fruitcake\laravel-cors\src\HandleCors.php(38): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #56 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #58 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #59 C:\xampp\htdocs\mansultan\public\index.php(52): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #60 {main} [] []
[2025-06-04T05:27:02.113922+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\mansultan\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(43): Illuminate\Support\Facades\Facade::__callStatic('getLocation', Array) #4 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckRestriction.php(27): App\Http\Middleware\CheckRestriction->checkIpRestriction(Object(App\Models\IpRestriction), '::1') #5 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckRestriction->handle(Object(Illuminate\Http\Request), Object(Closure)) #6 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMaintenance.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #7 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #8 C:\xampp\htdocs\mansultan\app\Http\Middleware\Share.php(90): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #9 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Share->handle(Object(Illuminate\Http\Request), Object(Closure)) #10 C:\xampp\htdocs\mansultan\app\Http\Middleware\Impersonate.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Impersonate->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\mansultan\app\Http\Middleware\CheckMobileApp.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\CheckMobileApp->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\mansultan\app\Http\Middleware\DebugBar.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\DebugBar->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\mansultan\app\Http\Middleware\SessionValidity.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SessionValidity->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\mansultan\app\Http\Middleware\UserLocale.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\UserLocale->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #24 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #25 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #26 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #27 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #28 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #33 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(799): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #35 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #37 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #40 C:\xampp\htdocs\mansultan\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #44 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\mansultan\vendor\fruitcake\laravel-cors\src\HandleCors.php(38): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #56 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #58 C:\xampp\htdocs\mansultan\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #59 C:\xampp\htdocs\mansultan\public\index.php(52): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #60 {main} [] []
