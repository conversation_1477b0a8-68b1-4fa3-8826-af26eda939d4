<?php
// Fix for missing advertising_banners table
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

try {
    echo "Checking database connection...\n";
    DB::connection()->getPdo();
    echo "✅ Database connected successfully!\n\n";
    
    // Check if table exists
    if (Schema::hasTable('advertising_banners')) {
        echo "✅ Table 'advertising_banners' already exists!\n";
    } else {
        echo "❌ Table 'advertising_banners' does not exist. Creating...\n";
        
        // Read and execute SQL file
        $sql = file_get_contents('fix_advertising_table.sql');
        
        // Split SQL statements
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                DB::statement($statement);
            }
        }
        
        echo "✅ Table 'advertising_banners' created successfully!\n";
    }
    
    // Check if translations table exists
    if (Schema::hasTable('advertising_banners_translations')) {
        echo "✅ Table 'advertising_banners_translations' already exists!\n";
    } else {
        echo "✅ Table 'advertising_banners_translations' created successfully!\n";
    }
    
    echo "\n🎉 All advertising tables are now ready!\n";
    echo "You can now access the website at: http://localhost:8000\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Please check your database settings in .env file\n";
}
?>
